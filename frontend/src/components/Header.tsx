import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  TextField,
  InputAdornment,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  useMediaQuery,
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  NotificationsNone as NotificationIcon,
  HelpOutline as HelpIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  ExitToApp as LogoutIcon,
  GridView as GridIcon,
  Brightness7 as Brightness7Icon,
  Brightness4 as Brightness4Icon,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useApp } from '../contexts/AppContext';
import { fileApi } from '../services/api';

interface HeaderProps {
  onUploadClick: () => void;
  onHomeClick?: () => void;
  onToggleTheme: () => void;
  onMobileMenuClick?: () => void;
  user?: any;
  onLogout?: () => void;
}


const Header: React.FC<HeaderProps> = ({ onUploadClick, onHomeClick, onToggleTheme, onMobileMenuClick, user, onLogout }) => {
  const { state, dispatch } = useApp();
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [searchInput, setSearchInput] = useState(state.searchQuery);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const open = Boolean(anchorEl);

  const handleSearch = async (query: string) => {
    if (query.trim().length < 2) {
      dispatch({ type: 'CLEAR_SEARCH' });
      return;
    }

    dispatch({ type: 'SET_SEARCHING', payload: true });
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });

    try {
      console.log('Searching for:', query);
      const response = await fileApi.search(query);
      console.log('Search response:', response);
      if (response.code === 200 && response.data) {
        const allItems = [...response.data.folders, ...response.data.files];
        console.log('Search results:', allItems);
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: allItems });
      }
    } catch (error) {
      console.error('Search error:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Search failed' });
    } finally {
      dispatch({ type: 'SET_SEARCHING', payload: false });
    }
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchInput(value);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Debounce search
    searchTimeoutRef.current = setTimeout(() => {
      handleSearch(value);
    }, 500);
  };

  const handleClearSearch = () => {
    setSearchInput('');
    dispatch({ type: 'CLEAR_SEARCH' });
  };

  const testSearch = () => {
    console.log('Testing search with "heyu"');
    setSearchInput('heyu');
    handleSearch('heyu');
  };

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileClose = () => {
    setAnchorEl(null);
  };

  return (
    <AppBar
      position="fixed"
      elevation={0}
      sx={{
        backgroundColor: theme.palette.background.paper,
        borderBottom: `1px solid ${theme.palette.divider}`,
        height: 64,
      }}
    >
      <Toolbar sx={{ py: 1, minHeight: '64px !important', display: 'flex' }}>
        {/* Mobile Menu Button */}
        {isMobile && (
          <IconButton
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={onMobileMenuClick}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
        )}

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer',
            '&:hover': {
              opacity: 0.8,
            },
            transition: 'opacity 0.2s ease',
          }}
          onClick={() => {
            if (onHomeClick) {
              onHomeClick();
            } else {
              navigate('/');
            }
          }}
        >
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: 1,
              background: '#0061FF',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 2,
            }}
          >
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: 'white',
                borderRadius: 0.5,
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 4,
                  left: 4,
                  width: 12,
                  height: 8,
                  backgroundColor: '#0061FF',
                  borderRadius: '2px 2px 0 0',
                },
              }}
            />
          </Box>
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              color: theme.palette.text.primary,
              fontSize: '1.25rem',
            }}
          >
            TeleStore
          </Typography>
        </Box>

        <Box sx={{ flexGrow: 1, maxWidth: 600, mx: 12 }}>
          <TextField
            fullWidth
            size="small"
            placeholder={t('header.searchPlaceholder')}
            value={searchInput}
            onChange={handleSearchInputChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#637381' }} />
                </InputAdornment>
              ),
              endAdornment: searchInput && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={handleClearSearch}>
                    <ClearIcon sx={{ color: '#637381' }} />
                  </IconButton>
                  <IconButton onClick={testSearch} sx={{ ml: 1 }}>
                    🔍
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                backgroundColor: '#F9FAFB',
                borderRadius: 2,
                '&:hover': {
                  backgroundColor: '#F3F4F6',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid #E5E7EB',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  border: '1px solid #D1D5DB',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  border: '2px solid #0061FF',
                },
                '& input': {
                  color: '#1E1E1E',
                  fontSize: '0.875rem',
                  '&::placeholder': {
                    color: '#9CA3AF',
                  },
                },
              },
            }}
          />
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto', gap: 1 }}>
          {/* <IconButton
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <GridIcon />
          </IconButton> */}

          <IconButton
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <NotificationIcon />
          </IconButton>

          {/* <IconButton
            onClick={onToggleTheme}
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            {theme.palette.mode === 'dark' ? (
              <Brightness7Icon />
            ) : (
              <Brightness4Icon />
            )}
          </IconButton> */}

          <IconButton
            onClick={() => navigate('/settings')}
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <SettingsIcon />
          </IconButton>

          <IconButton
            sx={{
              color: theme.palette.text.secondary,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <HelpIcon />
          </IconButton>

          <IconButton
            onClick={handleProfileClick}
            sx={{
              ml: 1,
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            <Avatar
              src={user?.avatar || user?.profilePicture}
              sx={{
                width: 32,
                height: 32,
                backgroundColor: '#0061FF',
                fontSize: '0.875rem',
                fontWeight: 600,
              }}
            >
              {user?.name?.charAt(0)?.toUpperCase() || user?.username?.charAt(0)?.toUpperCase() || 'U'}
            </Avatar>
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleProfileClose}
            PaperProps={{
              sx: {
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                border: '1px solid #E5E7EB',
                borderRadius: 2,
                mt: 1,
                minWidth: 200,
              },
            }}
          >
            <MenuItem onClick={handleProfileClose}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Profile</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => {
              handleProfileClose();
              navigate('/settings');
            }}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Settings</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => {
              handleProfileClose();
              onLogout?.();
            }}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Sign out</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
